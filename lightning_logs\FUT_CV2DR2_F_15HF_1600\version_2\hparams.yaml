model_name: time_series_model2dr_v2
loss: mse
lr: 0.001
model_path: pyqlab.models.fintimeseries
optimizer: adam
lr_scheduler: reduce_on_plateau
weight_decay: 0.0
num_classes: 3
ds_name: 15HF
ds_files:
- top.2025
start_time: ''
end_time: ''
direct: ls
filter_win: 0
is_normal: true
verbose: false
fut_codes:
- AG
- RB
- HC
- BU
- RU
- SP
- SC
- SS
- AO
- BR
- SR
- CF
- FG
- TA
- MA
- OI
- RM
- CY
- SF
- SM
- AP
- UR
- SA
- PK
- PX
- SH
- PR
- M
- 'Y'
- A
- P
- JM
- I
- C
- CS
- L
- V
- PP
- EG
- EB
data_path: f:/featdata/top
model_type: 0
seq_len: 15
pred_len: 1
embed_time: fixed
batch_size: 64
num_workers: 0
seed: 7435
version: CV2DR2
num_embeds:
- 72
- 5
- 11
num_channel: 15
num_input: 51
out_channels:
- 32
- 64
- 1600
- 1600
ins_nums:
- 0
- 51
- 51
- 17
dropout: 0.3
kernel_size: 3
activation: relu
pooling: max
num_conv_layers: 2
conv_channels: null
use_residual: false
use_attention: false
num_outputs: 1
probabilistic: false
inference_mode: false
lr_decay_steps: 5
lr_decay_rate: 0.1
lr_decay_min_lr: 1.0e-06
resume: false
max_epochs: 1
early_stop: 5
min_delta: 0.01
k_folds: 5
log_dir: lightning_logs
sub_dir: ''
model_dir: model
save_as_to_onnx: false
best_score: 0.6
