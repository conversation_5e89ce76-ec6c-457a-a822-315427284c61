# TimeSeriesModel2drV2 模型回测 PowerShell 脚本

param(
    [string]$ModelPath = "./model/best_model.ckpt",
    [string]$DataPath = "e:/featdata/main",
    [string]$OutputDir = "./backtest_results",
    [double]$InitialCapital = 10000.0,
    [double]$Leverage = 1.0,
    [double]$Commission = 0.001,
    [double]$Threshold = 0.5,
    [double]$StopLoss = $null,
    [double]$TakeProfit = $null,
    [string]$ModelType = "pytorch",
    [switch]$SaveChart,
    [switch]$RunExample,
    [switch]$Help
)

function Show-Help {
    Write-Host "TimeSeriesModel2drV2 模型回测脚本" -ForegroundColor Green
    Write-Host "=================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\run_backtest.ps1 [参数]"
    Write-Host ""
    Write-Host "参数:" -ForegroundColor Yellow
    Write-Host "  -ModelPath <路径>        模型文件路径 (默认: ./model/best_model.ckpt)"
    Write-Host "  -DataPath <路径>         数据目录路径 (默认: e:/featdata/main)"
    Write-Host "  -OutputDir <路径>        输出目录路径 (默认: ./backtest_results)"
    Write-Host "  -InitialCapital <数值>   初始资金 (默认: 10000.0)"
    Write-Host "  -Leverage <数值>         杠杆倍数 (默认: 1.0)"
    Write-Host "  -Commission <数值>       手续费率 (默认: 0.001)"
    Write-Host "  -Threshold <数值>        信号阈值 (默认: 0.5)"
    Write-Host "  -StopLoss <数值>         止损比例 (可选)"
    Write-Host "  -TakeProfit <数值>       止盈比例 (可选)"
    Write-Host "  -ModelType <类型>        模型类型 (pytorch/onnx, 默认: pytorch)"
    Write-Host "  -SaveChart               保存图表"
    Write-Host "  -RunExample              运行示例回测"
    Write-Host "  -Help                    显示帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  # 基本回测"
    Write-Host "  .\run_backtest.ps1 -ModelPath './my_model.ckpt'"
    Write-Host ""
    Write-Host "  # 带杠杆和止损的回测"
    Write-Host "  .\run_backtest.ps1 -Leverage 2.0 -StopLoss 0.05 -TakeProfit 0.10 -SaveChart"
    Write-Host ""
    Write-Host "  # 运行示例回测"
    Write-Host "  .\run_backtest.ps1 -RunExample"
}

function Test-Prerequisites {
    Write-Host "检查运行环境..." -ForegroundColor Blue
    
    # 检查Python
    try {
        $pythonVersion = python --version 2>&1
        Write-Host "✓ Python: $pythonVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ 错误: 未找到Python" -ForegroundColor Red
        return $false
    }
    
    # 检查必要的Python包
    $requiredPackages = @("torch", "numpy", "pandas", "matplotlib", "tqdm")
    foreach ($package in $requiredPackages) {
        try {
            python -c "import $package" 2>$null
            Write-Host "✓ $package 已安装" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ 错误: 缺少Python包 $package" -ForegroundColor Red
            return $false
        }
    }
    
    return $true
}

function Run-Backtest {
    param(
        [hashtable]$Config
    )
    
    Write-Host "开始运行回测..." -ForegroundColor Blue
    Write-Host "配置参数:" -ForegroundColor Yellow
    
    foreach ($key in $Config.Keys) {
        if ($Config[$key] -ne $null) {
            Write-Host "  $key : $($Config[$key])"
        }
    }
    Write-Host ""
    
    # 构建Python命令
    $pythonArgs = @(
        "backtest_time_series_model2dr_v2.py"
        "--model_path", "`"$($Config.ModelPath)`""
        "--model_type", $Config.ModelType
        "--data_path", "`"$($Config.DataPath)`""
        "--ds_files", "`"[`"main.2024`"]`""
        "--fut_codes", "`"[`"IF`", `"IH`", `"IC`"]`""
        "--seq_len", "30"
        "--initial_capital", $Config.InitialCapital
        "--commission", $Config.Commission
        "--threshold", $Config.Threshold
        "--leverage", $Config.Leverage
        "--output_dir", "`"$($Config.OutputDir)`""
        "--print_interval", "100"
    )
    
    if ($Config.StopLoss -ne $null) {
        $pythonArgs += "--stop_loss", $Config.StopLoss
    }
    
    if ($Config.TakeProfit -ne $null) {
        $pythonArgs += "--take_profit", $Config.TakeProfit
    }
    
    if ($Config.SaveChart) {
        $pythonArgs += "--save_chart"
    }
    
    # 执行Python脚本
    try {
        & python @pythonArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "回测完成！" -ForegroundColor Green
            Write-Host "结果已保存到: $($Config.OutputDir)" -ForegroundColor Green
            
            # 检查输出文件
            $resultsFile = Join-Path $Config.OutputDir "backtest_results.json"
            if (Test-Path $resultsFile) {
                Write-Host "✓ 结果文件: $resultsFile" -ForegroundColor Green
            }
            
            $chartFile = Join-Path $Config.OutputDir "backtest_chart.png"
            if (Test-Path $chartFile) {
                Write-Host "✓ 图表文件: $chartFile" -ForegroundColor Green
            }
        }
        else {
            Write-Host "回测失败，错误代码: $LASTEXITCODE" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "执行回测时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Run-Example {
    Write-Host "运行示例回测..." -ForegroundColor Blue
    
    try {
        python example_backtest.py
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "示例回测完成！" -ForegroundColor Green
        }
        else {
            Write-Host "示例回测失败，错误代码: $LASTEXITCODE" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "运行示例时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 主程序
Write-Host "TimeSeriesModel2drV2 模型回测系统" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

if ($Help) {
    Show-Help
    exit 0
}

# 检查运行环境
if (-not (Test-Prerequisites)) {
    Write-Host "环境检查失败，请安装必要的依赖" -ForegroundColor Red
    exit 1
}

# 运行示例
if ($RunExample) {
    Run-Example
    exit 0
}

# 检查模型文件
if (-not (Test-Path $ModelPath)) {
    Write-Host "警告: 模型文件不存在: $ModelPath" -ForegroundColor Yellow
    Write-Host "将尝试运行，但可能会使用示例模型" -ForegroundColor Yellow
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "已创建输出目录: $OutputDir" -ForegroundColor Green
}

# 准备配置
$config = @{
    ModelPath = $ModelPath
    DataPath = $DataPath
    OutputDir = $OutputDir
    InitialCapital = $InitialCapital
    Leverage = $Leverage
    Commission = $Commission
    Threshold = $Threshold
    StopLoss = $StopLoss
    TakeProfit = $TakeProfit
    ModelType = $ModelType
    SaveChart = $SaveChart.IsPresent
}

# 运行回测
Run-Backtest -Config $config

Write-Host ""
Write-Host "脚本执行完成" -ForegroundColor Cyan
