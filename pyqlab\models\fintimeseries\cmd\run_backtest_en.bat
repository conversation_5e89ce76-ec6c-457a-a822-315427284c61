@echo off
chcp 65001 >nul
echo ========================================
echo TimeSeriesModel2drV2 Backtest Script
echo ========================================

REM Set default parameters
set MODEL_PATH=./model/best_model.ckpt
set DATA_PATH=e:/featdata/main
set OUTPUT_DIR=./backtest_results
set INITIAL_CAPITAL=10000
set LEVERAGE=1.0
set COMMISSION=0.001
set THRESHOLD=0.5

REM Check if model path parameter is provided
if "%1"=="" (
    echo Using default model path: %MODEL_PATH%
) else (
    set MODEL_PATH=%1
    echo Using specified model path: %MODEL_PATH%
)

REM Check if model file exists
if not exist "%MODEL_PATH%" (
    echo WARNING: Model file does not exist: %MODEL_PATH%
    echo Will try to run with example model if available
    echo.
)

echo.
echo Backtest Parameters:
echo - Model Path: %MODEL_PATH%
echo - Data Path: %DATA_PATH%
echo - Output Directory: %OUTPUT_DIR%
echo - Initial Capital: %INITIAL_CAPITAL%
echo - Leverage: %LEVERAGE%
echo - Commission Rate: %COMMISSION%
echo - Signal Threshold: %THRESHOLD%
echo.

REM Create output directory
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Run backtest script
echo Starting backtest...
echo.

python backtest_time_series_model2dr_v2.py ^
    --model_path "%MODEL_PATH%" ^
    --model_type pytorch ^
    --data_path "%DATA_PATH%" ^
    --ds_files "[\"main.2024\"]" ^
    --fut_codes "[\"IF\", \"IH\", \"IC\"]" ^
    --seq_len 30 ^
    --initial_capital %INITIAL_CAPITAL% ^
    --commission %COMMISSION% ^
    --threshold %THRESHOLD% ^
    --leverage %LEVERAGE% ^
    --output_dir "%OUTPUT_DIR%" ^
    --save_chart ^
    --print_interval 100

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Backtest completed successfully!
    echo Results saved to: %OUTPUT_DIR%
    echo ========================================
    echo.
    echo Output files:
    if exist "%OUTPUT_DIR%\backtest_results.json" (
        echo - Results: %OUTPUT_DIR%\backtest_results.json
    )
    if exist "%OUTPUT_DIR%\backtest_chart.png" (
        echo - Chart: %OUTPUT_DIR%\backtest_chart.png
    )
) else (
    echo.
    echo ========================================
    echo Backtest failed with error code: %ERRORLEVEL%
    echo ========================================
    echo.
    echo Possible solutions:
    echo 1. Check if Python is installed and in PATH
    echo 2. Check if required packages are installed
    echo 3. Verify model file path is correct
    echo 4. Ensure data path exists and contains data files
)

echo.
echo Press any key to exit...
pause >nul
