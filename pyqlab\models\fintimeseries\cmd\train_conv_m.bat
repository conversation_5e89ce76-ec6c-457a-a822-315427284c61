e:
cd e:\lab\RoboQuant\pylab
@REM python ./pyqlab/pl/train_conv_m.py --batch_size=512 --k_folds=4 --max_epochs=8 --activation=relu --out_channels="(16, 32, 800, 800)" --model_name=time_series_model2dm
@REM python ./pyqlab/pl/train_conv_m.py --batch_size=512 --k_folds=4 --max_epochs=8 --activation=relu --out_channels="(24, 48, 1200, 1200)" --model_name=time_series_model2dm --dropout=0.5
@REM python ./pyqlab/pl/train_conv_m.py --ds_files="['main.2022']" --batch_size=512 --out_channels="(24, 48, 1200, 256)" --model_name=time_series_model2dm --restart=True --sub_dir=TimeSeriesConv2d_1200

@REM 备选
python ./pyqlab/pl/train_conv_m.py --batch_size=512 --k_folds=3 --max_epochs=10 --activation=relu --out_channels="(16, 32, 800, 800)" --model_name=time_series_model2dm --dropout=0.5
python ./pyqlab/pl/train_conv_m.py --batch_size=512 --k_folds=3 --max_epochs=10 --activation=relu --out_channels="(24, 48, 1200, 1200)" --model_name=time_series_model2dm --dropout=0.5

PAUSE