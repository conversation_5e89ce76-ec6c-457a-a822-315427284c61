{"total_return": -Infinity, "annualized_return": -0.6866714275684414, "max_drawdown": Infinity, "sharpe_ratio": 0, "total_trades": 1, "win_rate": 0.0, "avg_profit": 0, "avg_loss": 0, "profit_loss_ratio": 0, "final_capital": -Infinity, "trades": [{"datetime": "2025-06-04T10:46:08.745423", "action": "SELL", "price": 112.99999952316284, "position": -79.64601803520515, "margin": 9000.0, "available_capital": 1000.0}], "signals": ["SELL", "SELL", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "HOLD", "SELL", "SELL", "SELL", "HOLD", "HOLD", "SELL", "HOLD", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "SELL", "HOLD", "SELL", "SELL", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD", "HOLD"], "positions_history": [-79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515, -79.64601803520515], "equity_curve": [10000.0, 10000.0, -841176.9933560338, 74385001.72190112, -5450492664.1897745, 481862159847.67163, -35308130141439.64, 2952759570650412.0, -2.5398958495851347e+17, 2.1847599753336123e+19, -1.6008683811732353e+21, 1.3770301508763346e+23, -1.0419121973841768e+25, 8.962289012868996e+26, -7.4236305701171595e+28, 5.61699483120014e+30, -4.831609725598119e+32, 4.040593993979755e+34, -3.3790808346309835e+36, 2.3952599661713167e+38, -1.774188141153028e+40, 1.511985121784024e+42, -1.3005748160552384e+44, 9.219118873928736e+45, -6.608394961973237e+47, 6.158088438014983e+49, -5.247999285384573e+51, 3.7200419895104706e+53, -2.5480641681731283e+55, 2.2323748146490356e+57, -1.635757838868956e+59, 1.4070412596968623e+61, -1.1766849530689387e+63, 8.996954023791855e+64, -7.524001410967669e+66, 6.6517499444588745e+68, -5.668703739228779e+70, 4.830939580029641e+72, -3.693744972253129e+74, 3.0890168283058725e+76, -2.2634565925676975e+78, 1.8928906995754393e+80, -1.3870031052287496e+82, 9.610826919728159e+83, -8.726290678236227e+85, 7.436653062781226e+87, -6.337607904135449e+89, 4.795269713851097e+91, -4.1247806847916855e+93, 3.548041447697154e+95, -3.0519436247827274e+97, 2.552289150120733e+99, -1.9311568414604046e+101, 1.6611366855863457e+103, -1.256877761996201e+105, 1.0410952116649648e+107, -7.62855611125337e+108, 6.379633239531187e+110, -4.5222002211064735e+112, 3.7818400265585907e+114, -2.8916015927238575e+116, 2.556380532539584e+118, -1.873170879636105e+120, 1.6410966205108145e+122, -1.254785387101908e+124, 1.0493559262446207e+126, -7.689085949431353e+127, 5.4504051996538214e+129, -3.906927631008598e+131, 2.893892424695072e+133, -2.1435291760307226e+135, 1.792597417109869e+137, -1.3135138677125367e+139, 9.729301872233398e+140, -7.206571414879082e+142, 6.198926932056227e+144, -5.134686396372279e+146, 4.416739106257635e+148, -3.236336285417121e+150, 2.3714039476472828e+152, -1.6998559314332242e+154, 1.245558159204969e+156, -1.0714004724350064e+158, 8.959942048158799e+159, -7.707136011629539e+161, 6.383964009844882e+163, -4.7286530062617945e+165, 3.3519036075535107e+167, -2.4026919726371847e+169, 1.6074647316036525e+171, -1.5235351604982732e+173, 1.310509893629607e+175, -9.393920500547092e+176, 6.658876422769477e+178, -4.773176917056026e+180, 3.5355301727029644e+182, -2.4216817374539528e+184, 1.774471230947332e+186, -1.2295672454070786e+188, 9.107502552378097e+189, -6.528386802072496e+191, 4.939620119423278e+193, -4.1309212706770257e+195, 3.026905162125648e+197, -2.242052770423212e+199, 1.9285622110082042e+201, -1.4285013954939928e+203, 1.080857254286202e+205, -8.178167748580405e+206, 7.295215186240915e+208, -5.34552054109494e+210, 4.044619538352187e+212, -3.4790886737688534e+214, 2.5492791501199173e+216, -1.7867514021898672e+218, 1.4942301622943182e+220, -1.285302408384225e+222, 9.417968147800403e+223, -6.75093293741487e+225, 5.000469814734163e+227, -3.783541332996428e+229, 3.1641120153153526e+231, -2.2680802999797173e+233, 1.716113861190142e+235, -1.271139210103642e+237, 8.6054999381751e+238, -5.825847287009077e+240, 5.196862048750039e+242, -3.932138997297713e+244, 2.975202525875851e+246, -2.251148821560299e+248, 1.7033028752624102e+250, -1.4515758196957657e+252, 1.2370509032345873e+254, -9.064408444170819e+255, 6.714079555181361e+257, -5.614871868072852e+259, 4.695622957065828e+261, -3.4032878180868045e+263, 2.5208424025187044e+265, -1.7668205136295476e+267, 1.2664819643446896e+269, -1.0994856706626872e+271, 9.545092421573323e+272, -8.134446056038377e+274, 6.154824249386487e+276, -4.6569688064641355e+278, 4.15418105903546e+280, -3.143207803254607e+282, 2.3281990633475665e+284, -1.7245155961244007e+286, 1.2361572003915627e+288, -8.664039836605686e+289, 6.555534589156987e+291, -5.6911322444620624e+293, 4.850053430150712e+295, -4.2491618784205454e+297, 3.688874164163005e+299, -3.143704279706865e+301, 2.2284133985464748e+303, -1.6506035591649413e+305, 1.2489080070216518e+307, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity, Infinity, -Infinity]}