from datetime import datetime, date
import time
import pytz
import pandas as pd
import sys
import numpy as np
import json
from argparse import ArgumentParser

# sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

# ### FactorKvDB

class FactorsKvDB():
    def __init__(self,
        # dbfile="e:/featdata/kv.db",
        key_prefix='ffs:', #fsfs 股指期货因子 ffs 商品期货因子
        years=[],
        dbfile="d:/RoboQuant2/store/kv.db",
        save_path="e:/featdata",
        save_file="",
    ) -> None:
        self.years = years
        self._dbfile=dbfile
        self._save_path = save_path
        self._save_file = save_file
        self._db=None
        self._key_prefix=key_prefix
        self._keys=[]
        self._ls_col_names=[]
        self._ct_col_names=[]
        self._tz=pytz.timezone('Asia/Shanghai')

    def open_db(self, mode):
        if self._db:
            self.close_db()
        
        try:
            self._db=create_db("leveldb", self._dbfile, mode)
        except:
            raise 'Fail to open db!'
        self._ls_col_names, self._ct_col_names = self.get_factors_colnames()

    def close_db(self):
        if not self._db:
            raise "not db open."
        self._db.close()
        del self._db
        self._db=None

    def load_all_keys(self):
        if not self._db:
            raise "first open a db."
        self._keys.clear()
        cursor = self._db.new_cursor()
        while cursor.valid():
            key = cursor.key().decode('gbk', 'ignore')
            if len(key) >= 16 and key[0:len(self._key_prefix)] == self._key_prefix: #'ffs:'
                self._keys.append(key)
            cursor.next()
        del cursor

    def get_all_labels(self)->set:
        lbs = set()
        if len(self._keys) == 0:
            self.load_all_keys()
            
        for key in self._keys:
            pos0=key.find(':')
            pos1=key.find(':', pos0+1)
            lb=key[pos0+1:pos1]
            lbs.add(lb)
        return lbs

    def get_factors_colnames(self):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        ls_colnames = []
        ct_colnames = []
        while cursor.valid():
            key = cursor.key()
            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:
                value = cursor.value().decode()
                s2 = value.split('|')
                if len(s2) <= 3:
                    print(key)
                    cursor.next()
                    continue
                lf = json.loads(s2[0])
                ls_colnames.append('code')
                ls_colnames.append('date')
                for k, v in lf.items():
                    if isinstance(v, list):
                        ls_colnames.append(f'{k}_1')
                        ls_colnames.append(f'{k}_2')
                    else:
                        ls_colnames.append(f'{k}')
                ls_colnames.append('change')
                if len(s2) >= 4:
                    ct_colnames.append('code')
                    ct_colnames.append('date')
                    ct = json.loads(s2[len(s2) - 2])
                    for k, v in ct.items():
                        if isinstance(v, list):
                            ct_colnames.append(f'{k}_1')
                            ct_colnames.append(f'{k}_2')
                        else:
                            ct_colnames.append(f'{k}')
                del cursor
                return ls_colnames, ct_colnames
            cursor.next()
        return ls_colnames, ct_colnames

    def get_all_factors(self, year: int):
        if not self._db:
            raise "first open a db."
        if len(self._ls_col_names) == 0 or len(self._ct_col_names) == 0:
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        ldata = []
        sdata = []
        mdata = []
        cdata = []
        cursor = self._db.new_cursor()
        while cursor.valid():
            key = cursor.key()
            if len(key) >= 16 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:
                litem = []
                sitem = []
                mitem = []
                citem = []
                value = cursor.value().decode()
                s1 = key.decode().split(':')
                dt = date.fromtimestamp(int(s1[2]))
                if dt.year != year:
                    cursor.next()
                    continue
                litem.append(s1[1])
                litem.append(int(s1[2]))
                sitem.append(s1[1])
                sitem.append(int(s1[2]))
                mitem.append(s1[1])
                mitem.append(int(s1[2]))
                citem.append(s1[1])
                citem.append(int(s1[2]))
                s2 = value.split('|')
                assert len(s2) >= 4, f'invalid value: {value}'
                lf = json.loads(s2[0])
                sf = json.loads(s2[1])
                if len(s2) == 5:
                    mf = json.loads(s2[2])
                ct = json.loads(s2[len(s2) - 2])
                for _, v in lf.items():
                    if isinstance(v, list):
                        litem.append(v[1])
                        litem.append(v[2])
                    else:
                        litem.append(v)
                for _, v in sf.items():
                    if isinstance(v, list):
                        sitem.append(v[1])
                        sitem.append(v[2])
                    else:
                        sitem.append(v)
                if len(s2) == 5:
                    for _, v in mf.items():
                        if isinstance(v, list):
                            mitem.append(v[1])
                            mitem.append(v[2])
                        else:
                            mitem.append(v)
                    mitem.append(s2[len(s2)-1]) # change
                for _, v in ct.items():
                    if isinstance(v, list):
                        citem.append(v[1])
                        citem.append(v[2])
                    else:
                        citem.append(v)
                litem.append(s2[len(s2)-1]) # change
                sitem.append(s2[len(s2)-1]) # change
                ldata.append(litem)
                sdata.append(sitem)
                mdata.append(mitem)
                cdata.append(citem)
            cursor.next()
        del cursor
        ldf = pd.DataFrame(ldata, columns=self._ls_col_names)
        sdf = pd.DataFrame(sdata, columns=self._ls_col_names)
        mdf = pd.DataFrame(mdata, columns=self._ls_col_names)
        cdf = pd.DataFrame(cdata, columns=self._ct_col_names)
        # 修正Bar的时间,由于有休市和假期,所以时间不连续
        sdf['VOLUME_1'] = sdf['VOLUME_1'] % 21600
        sdf['VOLUME_2'] = sdf['VOLUME_2'] % 21600
        return ldf, sdf, mdf, cdf
    
    def get_single_factors(self, year: int, name: str):
        if not self._db:
            raise "first open a db."
        if self._ls_col_names == [] or self._ct_col_names == []:
            return pd.DataFrame()

        data = []
        cursor = self._db.new_cursor()
        while cursor.valid():
            key = cursor.key()
            if len(key) >= 16 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:
                item = []
                value = cursor.value().decode()
                s1 = key.decode().split(':')
                dt = date.fromtimestamp(int(s1[2]))
                if dt.year != year:
                    cursor.next()
                    continue
                item.append(s1[1])
                item.append(int(s1[2]))
                s2 = value.split('|')
                assert len(s2) >= 4, f'invalid value: {value}'
                if name == 'lf':
                    lf = json.loads(s2[0])
                    for _, v in lf.items():
                        if isinstance(v, list):
                            item.append(v[1])
                            item.append(v[2])
                        else:
                            item.append(v)
                    item.append(s2[len(s2)-1]) # change
                elif name == 'sf':
                    sf = json.loads(s2[1])
                    for _, v in sf.items():
                        if isinstance(v, list):
                            item.append(v[1])
                            item.append(v[2])
                        else:
                            item.append(v)
                    item.append(s2[len(s2)-1]) # change
                elif name == 'mf':
                    mf = json.loads(s2[2])
                    for _, v in mf.items():
                        if isinstance(v, list):
                            item.append(v[1])
                            item.append(v[2])
                        else:
                            item.append(v)
                    item.append(s2[len(s2)-1]) # change
                elif name == 'ct':
                    ct = json.loads(s2[len(s2)-2])
                    for _, v in ct.items():
                        if isinstance(v, list):
                            item.append(v[1])
                            item.append(v[2])
                        else:
                            item.append(v)
                data.append(item)
            cursor.next()
        del cursor

        if name == 'lf' or name == 'mf':
            df = pd.DataFrame(data, columns=self._ls_col_names)
        elif name == 'sf':
            df = pd.DataFrame(data, columns=self._ls_col_names)
            # 修正Bar的时间,由于有休市和假期,所以时间不连续
            df['VOLUME_1'] = df['VOLUME_1'] % 21600
            df['VOLUME_2'] = df['VOLUME_2'] % 21600
        elif name == 'ct':
            df = pd.DataFrame(data, columns=self._ct_col_names)
        return df
    

    def write(self, key, value):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.put(key, value)
        transaction.commit()
        del transaction
        
    def delete(self, key):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        transaction.delete(key)
        transaction.commit()
        del transaction

    def clear(self):
        if not self._db:
            raise "first open a db."
        transaction = self._db.new_transaction()
        cursor = self._db.new_cursor()
        while cursor.valid():
            key = cursor.key()
            if len(key) > 5 and key[0:len(self._key_prefix)].decode('gbk', 'ignore') == self._key_prefix:
                transaction.delete(key)
                print(f'del key: {key}')
            cursor.next()
        transaction.commit()
        del transaction
        del cursor

    def query(self):
        if not self._db:
            raise "first open a db."
        cursor = self._db.new_cursor()
        while cursor.valid():
            print(cursor.key())
            # print(cursor.key(), cursor.value())
            cursor.next()
        del cursor

    def export_all(self):
        def trans_timestamp(dt):
            # return int(time.mktime(dt.timetuple()))//300
            return int(dt//300)

        def log_return(series):
            return np.log(series).diff()
            
        if not self._db:
            raise "first open a db."
        # self.open_db(Mode.read)
        df=self.read_all()
        # self.close_db()
        print(df.shape)

        try:
            cols=df.columns
            df = df.groupby(["label", "datetime"]).agg({"mean"}).reset_index()
            df.columns = cols
            df["time_id"]=df["datetime"].apply(trans_timestamp)

            for lb in df["label"].unique():
                # df.to_parquet(f"../data/tickdata.parquet", engine='fastparquet')
                df2=df[df["label"]==lb]
                df2.to_parquet(f"{self._save_path}/tickdata.{lb}.parquet", engine='fastparquet')
                # df = pd.read_parquet(f"../data/tickdata.parquet")
                df2 = df2.groupby(['label','time_id'])['price'].apply(np.mean).reset_index()
                df2['return'] = log_return(df2['price'])
                df2=df2.fillna(0)
                df2['target'] = (df2['return']>0).astype(int)
                df2=df2.drop(['price', 'return'], axis=1)
                df2.to_parquet(f"{self._save_path}/tickdata_target.{lb}.parquet", engine='fastparquet')
        except:
            raise 'Fail to export all data!'

    def export_to_file(self):
        if not self._db:
            self.open_db(Mode.read)
        for year in self.years:
            ldf, sdf, mdf, cdf = self.get_all_factors(year)
            if ldf.empty or sdf.empty or mdf.empty or cdf.empty:
                print(f'empty {year}')
                continue
            print(year, ldf.shape, sdf.shape, mdf.shape, cdf.shape)
            ldf.to_parquet(f"{self._save_path}/ffs_lf.{self._save_file}.{year}.parquet", engine='fastparquet')
            sdf.to_parquet(f"{self._save_path}/ffs_sf.{self._save_file}.{year}.parquet", engine='fastparquet')
            mdf.to_parquet(f"{self._save_path}/ffs_mf.{self._save_file}.{year}.parquet", engine='fastparquet')
            cdf.to_parquet(f"{self._save_path}/ffs_ct.{self._save_file}.{year}.parquet", engine='fastparquet')
        self.close_db()
        
    def export_to_file_by_single(self):
        if not self._db:
            self.open_db(Mode.read)
        for year in self.years:
            for name in ['lf', 'sf', 'mf', 'ct']: # 
                df = self.get_single_factors(year, name)
                if df.empty:
                    print(f'empty {name} {year}')
                    continue
                print(name, year, df.shape)
                df.to_parquet(f"{self._save_path}/ffs_{name}.{self._save_file}.{year}.parquet", engine='fastparquet')
        self.close_db()
                
def merge_data():
    # ### 合并
    # 将按月份导出的数据合并成一个文件
    dtype = ['lf', 'sf', 'mf', 'ct']
    for dt in dtype:
        df = pd.read_parquet(f'e:/featdata/ffs_{dt}.main.2023.parquet')
        dfa = pd.read_parquet(f'e:/featdata/ffs_{dt}.main.12.2023.parquet')
        print(df.shape, dfa.shape)
        df = pd.concat([df,dfa],axis=0)
        print(df.shape)
        df = df.drop_duplicates(subset=['code', 'date'], keep='last')
        print(df.shape)
        df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
        df.reset_index(drop=True, inplace=True)
        df.to_parquet(f'e:/featdata/ffs_{dt}.main.2023.A.parquet', engine='fastparquet')


def main(args):

    print(args)
    assert args.is_sf and args.key_prefix == "fsfs:" or \
        not args.is_sf and args.key_prefix == "ffs:", "key_prefix error."

    ff = FactorsKvDB(key_prefix=args.key_prefix, # ffs: or fsfs:
                    years=args.years,
                    dbfile=args.dbfile,
                    save_path=args.save_path,
                    save_file=args.save_file,
                    )
    if args.is_sf:
        ff.export_to_file()
    else:
        ff.export_to_file_by_single()




if __name__ == '__main__':
    parser = ArgumentParser()

    # parser.add_argument('--is_sf', action='store_true', default=False)
    # parser.add_argument('--dbfile', type=str, default="d:/RoboQuant2/store/kv.db")
    # parser.add_argument('--key_prefix', type=str, default="ffs:", choices=['ffs:', 'fsfs:'])
    # parser.add_argument('--save_path', type=str, default="f:/featdata/top")
    # parser.add_argument('--save_file', type=str, default="top")
    # parser.add_argument('--years', type=list, default=[2024, 2025])

    parser.add_argument('--is_sf', action='store_true', default=True)
    parser.add_argument('--dbfile', type=str, default="d:/RoboQuant2/store/kv.db")
    parser.add_argument('--key_prefix', type=str, default="fsfs:", choices=['ffs:', 'fsfs:'])
    parser.add_argument('--save_path', type=str, default="F:/featdata/sf")
    parser.add_argument('--save_file', type=str, default="sf")
    parser.add_argument('--years', type=list, default=[2024, 2025])

    args = parser.parse_args()

    main(args)

    # merge_data()

