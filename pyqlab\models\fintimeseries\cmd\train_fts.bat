e:
cd e:\lab\RoboQuant\pylab

@REM =================FUT==================

@REM python .\pyqlab\pl\train_fts.py ^
@REM --model_name Transformer ^
@REM --model_path pyqlab.models.transformer ^
@REM --data_path="e:/featdata/main" ^
@REM --ds_files="['main.2024']" ^
@REM --ins_nums="(0, 51, 51, 17)" ^
@REM --enc_in=120 ^
@REM --dec_in=120 ^
@REM --c_out=120 ^
@REM --seq_len=16 ^
@REM --label_len=8 ^
@REM --pred_len=3 ^
@REM --batch_size=64 ^
@REM --d_layers 1 ^
@REM --e_layers 2 ^
@REM --n_heads 8 ^
@REM --d_model 256 ^
@REM --factor 1 ^
@REM --batch_size 64 ^
@REM --max_epochs 2 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 3 ^
@REM --k_folds 5 ^
@REM --start_time=2024-05-01 ^
@REM --end_time=2024-06-30

@REM python .\pyqlab\pl\train_fts.py ^
@REM --model_name Autoformer ^
@REM --model_path pyqlab.models.transformer ^
@REM --data_path="e:/featdata/main" ^
@REM --ds_files="['main.2024']" ^
@REM --ins_nums="(0, 51, 51, 17)" ^
@REM --enc_in=120 ^
@REM --dec_in=120 ^
@REM --c_out=120 ^
@REM --seq_len=16 ^
@REM --label_len=8 ^
@REM --pred_len=3 ^
@REM --batch_size=64 ^
@REM --d_layers 1 ^
@REM --e_layers 2 ^
@REM --n_heads 8 ^
@REM --d_model 256 ^
@REM --factor 1 ^
@REM --batch_size 64 ^
@REM --max_epochs 2 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 3 ^
@REM --k_folds 5 ^
@REM --start_time=2024-05-01 ^
@REM --end_time=2024-06-30

@REM python .\pyqlab\pl\train_fts.py ^
@REM --model_name Nonstationary_Transformer ^
@REM --model_path pyqlab.models.transformer ^
@REM --data_path="e:/featdata/main" ^
@REM --ds_files="['main.2024']" ^
@REM --ins_nums="(0, 51, 51, 17)" ^
@REM --enc_in=120 ^
@REM --dec_in=120 ^
@REM --c_out=120 ^
@REM --seq_len=16 ^
@REM --label_len=8 ^
@REM --pred_len=3 ^
@REM --batch_size=128 ^
@REM --d_layers 1 ^
@REM --e_layers 2 ^
@REM --n_heads 8 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --max_epochs 5 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 3 ^
@REM --k_folds 5 ^
@REM --start_time=2024-05-01 ^
@REM --end_time=2024-06-30

@REM =================SF==================

@REM python .\pyqlab\pl\train_fts.py ^
@REM --model_name Transformer ^
@REM --model_path pyqlab.models.transformer ^
@REM --fut_codes=SF_FUT_CODES ^
@REM --data_path="e:/featdata/sf2024" ^
@REM --ds_files="['sf.2024']" ^
@REM --start_time=2024-03-01 ^
@REM --end_time=2024-06-30 ^
@REM --ins_nums="(0, 51, 51, 0)" ^
@REM --enc_in=103 ^
@REM --dec_in=103 ^
@REM --c_out=103 ^
@REM --seq_len=15 ^
@REM --label_len=12 ^
@REM --pred_len=3 ^
@REM --d_layers 1 ^
@REM --e_layers 2 ^
@REM --n_heads 8 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --max_epochs 1 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 3 ^
@REM --k_folds 3

@REM python .\pyqlab\pl\train_fts.py ^
@REM --model_name iTransformer ^
@REM --model_path pyqlab.models.transformer ^
@REM --fut_codes=SF_FUT_CODES ^
@REM --data_path="e:/featdata/sf2024" ^
@REM --ds_files="['sf.2024']" ^
@REM --start_time=2024-03-01 ^
@REM --end_time=2024-06-30 ^
@REM --ins_nums="(0, 51, 51, 0)" ^
@REM --enc_in=103 ^
@REM --dec_in=103 ^
@REM --c_out=103 ^
@REM --seq_len=15 ^
@REM --label_len=12 ^
@REM --pred_len=3 ^
@REM --d_layers 1 ^
@REM --e_layers 2 ^
@REM --n_heads 8 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --max_epochs 1 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 3 ^
@REM --k_folds 3

@REM python .\pyqlab\pl\train_fts.py ^
@REM --model_name Nonstationary_Transformer ^
@REM --model_path pyqlab.models.transformer ^
@REM --fut_codes=SF_FUT_CODES ^
@REM --data_path="e:/featdata/sf2024" ^
@REM --ds_files="['sf.2024']" ^
@REM --ins_nums="(0, 51, 51, 0)" ^
@REM --enc_in=103 ^
@REM --dec_in=103 ^
@REM --c_out=103 ^
@REM --seq_len=15 ^
@REM --label_len=12 ^
@REM --pred_len=3 ^
@REM --d_layers 1 ^
@REM --e_layers 2 ^
@REM --n_heads 8 ^
@REM --d_model 256 ^
@REM --batch_size 64 ^
@REM --max_epochs 1 ^
@REM --lr 0.0005 ^
@REM --min_delta 0.01 ^
@REM --early_stop 3 ^
@REM --k_folds 5

