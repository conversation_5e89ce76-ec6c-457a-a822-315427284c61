"""
测试ONNX模型回测功能

该脚本用于测试回测系统对ONNX模型的支持。
"""

import os
import sys
import numpy as np
import torch
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.fintimeseries.backtest_time_series_model2dr_v2 import TimeSeriesBacktester
from pyqlab.models.fintimeseries.test_backtest import MockDataset

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockONNXModel:
    """模拟ONNX模型，用于测试"""
    
    def __init__(self):
        self.input_info = [
            type('InputInfo', (), {'name': 'embds'})(),
            type('InputInfo', (), {'name': 'x'})()
        ]
    
    def get_inputs(self):
        """返回模型输入信息"""
        return self.input_info
    
    def run(self, output_names, input_dict):
        """模拟ONNX推理"""
        try:
            # 获取输入
            embds = input_dict.get('embds')
            x = input_dict.get('x')
            
            if embds is None or x is None:
                raise ValueError("Missing required inputs")
            
            # 模拟预测逻辑
            batch_size = embds.shape[0] if embds is not None else x.shape[0]
            
            # 生成随机预测值（限制在合理范围内）
            prediction = np.random.normal(0, 0.01, (batch_size, 1)).astype(np.float32)
            prediction = np.clip(prediction, -0.05, 0.05)
            
            return [prediction]
            
        except Exception as e:
            logger.error(f"模拟ONNX推理失败: {e}")
            # 返回默认值
            return [np.array([[0.0]], dtype=np.float32)]


def test_onnx_model_detection():
    """测试ONNX模型检测"""
    logger.info("测试ONNX模型检测...")
    
    mock_onnx_model = MockONNXModel()
    
    # 创建回测器
    backtester = TimeSeriesBacktester(
        model=mock_onnx_model,
        initial_capital=10000.0
    )
    
    # 验证模型类型检测
    assert backtester.model_type == 'ONNX', f"期望模型类型为ONNX，但得到 {backtester.model_type}"
    
    logger.info("✓ ONNX模型检测测试通过")


def test_onnx_prediction():
    """测试ONNX模型预测"""
    logger.info("测试ONNX模型预测...")
    
    mock_onnx_model = MockONNXModel()
    backtester = TimeSeriesBacktester(
        model=mock_onnx_model,
        initial_capital=10000.0
    )
    
    # 创建测试输入
    embds = torch.randint(0, 72, (1, 30, 3), dtype=torch.int64)
    x = torch.randn(1, 30, 51, dtype=torch.float32)
    
    # 测试预测
    prediction = backtester._predict_onnx(embds, x)
    
    # 验证预测结果
    assert isinstance(prediction, torch.Tensor), "预测结果应该是torch.Tensor"
    assert prediction.shape == (1, 1), f"预测结果形状应该是(1, 1)，但得到 {prediction.shape}"
    
    logger.info("✓ ONNX模型预测测试通过")


def test_onnx_backtest():
    """测试ONNX模型完整回测"""
    logger.info("测试ONNX模型完整回测...")
    
    # 创建模拟ONNX模型
    mock_onnx_model = MockONNXModel()
    
    # 创建模拟数据集
    dataset = MockDataset(size=50, seq_len=30)
    
    # 创建回测器
    backtester = TimeSeriesBacktester(
        model=mock_onnx_model,
        initial_capital=10000.0,
        leverage=1.0
    )
    
    # 执行回测
    results = backtester.backtest(
        dataset=dataset,
        seq_len=30,
        commission=0.001,
        threshold=0.01,  # 较小的阈值以产生更多交易
        print_interval=20
    )
    
    # 验证结果
    assert 'total_return' in results, "缺少总收益率"
    assert 'max_drawdown' in results, "缺少最大回撤"
    assert 'sharpe_ratio' in results, "缺少夏普比率"
    assert 'total_trades' in results, "缺少交易次数"
    assert 'win_rate' in results, "缺少胜率"
    assert 'equity_curve' in results, "缺少权益曲线"
    
    logger.info("✓ ONNX模型完整回测测试通过")
    
    # 打印结果摘要
    logger.info(f"回测结果摘要:")
    logger.info(f"  总收益率: {results['total_return']:.4f}")
    logger.info(f"  最大回撤: {results['max_drawdown']:.4f}")
    logger.info(f"  夏普比率: {results['sharpe_ratio']:.4f}")
    logger.info(f"  总交易次数: {results['total_trades']}")
    logger.info(f"  胜率: {results['win_rate']:.4f}")
    
    return results


def test_onnx_error_handling():
    """测试ONNX模型错误处理"""
    logger.info("测试ONNX模型错误处理...")
    
    class BrokenONNXModel:
        """故意损坏的ONNX模型，用于测试错误处理"""
        
        def get_inputs(self):
            return [type('InputInfo', (), {'name': 'input'})()]
        
        def run(self, output_names, input_dict):
            raise RuntimeError("模拟ONNX推理错误")
    
    broken_model = BrokenONNXModel()
    backtester = TimeSeriesBacktester(
        model=broken_model,
        initial_capital=10000.0
    )
    
    # 创建测试输入
    embds = torch.randint(0, 72, (1, 30, 3), dtype=torch.int64)
    x = torch.randn(1, 30, 51, dtype=torch.float32)
    
    # 测试错误处理
    prediction = backtester._predict_onnx(embds, x)
    
    # 验证错误处理
    assert isinstance(prediction, torch.Tensor), "即使出错也应该返回torch.Tensor"
    assert prediction.shape == (1, 1), "错误处理应该返回正确形状的默认值"
    assert prediction.item() == 0.0, "错误处理应该返回0.0作为默认预测值"
    
    logger.info("✓ ONNX模型错误处理测试通过")


def run_all_onnx_tests():
    """运行所有ONNX相关测试"""
    logger.info("=" * 60)
    logger.info("开始运行ONNX模型回测测试")
    logger.info("=" * 60)
    
    try:
        # 运行各项测试
        test_onnx_model_detection()
        test_onnx_prediction()
        test_onnx_error_handling()
        results = test_onnx_backtest()
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ 所有ONNX测试通过！ONNX模型回测功能正常。")
        logger.info("=" * 60)
        
        return results
        
    except Exception as e:
        logger.error(f"❌ ONNX测试失败: {e}")
        import traceback
        traceback.print_exc()
        raise


def compare_pytorch_vs_onnx():
    """比较PyTorch和ONNX模型的回测结果"""
    logger.info("比较PyTorch和ONNX模型回测结果...")
    
    # 创建相同的数据集
    dataset = MockDataset(size=100, seq_len=30)
    
    # 测试PyTorch模型
    from pyqlab.models.fintimeseries.test_backtest import MockModel
    pytorch_model = MockModel()
    pytorch_backtester = TimeSeriesBacktester(
        model=pytorch_model,
        initial_capital=10000.0
    )
    
    pytorch_results = pytorch_backtester.backtest(
        dataset=dataset,
        commission=0.001,
        threshold=0.01,
        print_interval=1000
    )
    
    # 测试ONNX模型
    onnx_model = MockONNXModel()
    onnx_backtester = TimeSeriesBacktester(
        model=onnx_model,
        initial_capital=10000.0
    )
    
    onnx_results = onnx_backtester.backtest(
        dataset=dataset,
        commission=0.001,
        threshold=0.01,
        print_interval=1000
    )
    
    # 比较结果
    logger.info("\n模型类型比较:")
    logger.info("指标\t\tPyTorch\t\tONNX")
    logger.info("-" * 50)
    logger.info(f"总收益率\t{pytorch_results['total_return']:.4f}\t\t{onnx_results['total_return']:.4f}")
    logger.info(f"最大回撤\t{pytorch_results['max_drawdown']:.4f}\t\t{onnx_results['max_drawdown']:.4f}")
    logger.info(f"夏普比率\t{pytorch_results['sharpe_ratio']:.4f}\t\t{onnx_results['sharpe_ratio']:.4f}")
    logger.info(f"交易次数\t{pytorch_results['total_trades']}\t\t{onnx_results['total_trades']}")
    logger.info(f"胜率\t\t{pytorch_results['win_rate']:.4f}\t\t{onnx_results['win_rate']:.4f}")
    
    logger.info("\n✓ PyTorch vs ONNX 比较完成")


if __name__ == "__main__":
    try:
        # 运行ONNX测试
        run_all_onnx_tests()
        
        # 可选：比较PyTorch和ONNX
        # compare_pytorch_vs_onnx()
        
    except KeyboardInterrupt:
        logger.info("用户中断了测试")
    except Exception as e:
        logger.error(f"测试执行出错: {e}")
        import traceback
        traceback.print_exc()
