"""
TimeSeriesModel2drV2模型回测脚本

该脚本用于对训练好的TimeSeriesModel2drV2模型进行回测，评估其在实际交易中的表现。
支持加载PyTorch和ONNX格式的模型。
"""

import os
import sys
import argparse
import json
import time
import logging
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
from tqdm import tqdm
from typing import Dict, List, Tuple, Union, Optional, Any

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和数据处理模块
from pyqlab.models.fintimeseries.time_series_model2dr_v2 import TimeSeriesModel2drV2
from pyqlab.models.pl_fts_model import PLFtsModel
from pyqlab.data import get_dataset
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TimeSeriesBacktester:
    """时间序列模型回测器"""
    
    def __init__(self, model, initial_capital=10000.0, device=None, leverage=1.0):
        """
        初始化回测器
        
        Args:
            model: TimeSeriesModel2drV2模型实例或PLFtsModel实例
            initial_capital: 初始资金
            device: 计算设备 (None表示自动选择)
            leverage: 杠杆倍数，默认为1.0（无杠杆）
        """
        self.model = model
        self.initial_capital = initial_capital
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.leverage = leverage
        
        # 检测模型类型
        if isinstance(model, PLFtsModel):
            self.model_type = 'PLFtsModel'
            self.core_model = model.model
        else:
            self.model_type = 'TimeSeriesModel2drV2'
            self.core_model = model
            
        logger.info(f"检测到模型类型: {self.model_type}")
        logger.info(f"使用杠杆倍数: {self.leverage}倍")
    
    def _generate_signal(self, prediction, threshold=0.5):
        """
        根据模型预测生成交易信号
        
        Args:
            prediction: 模型预测值（价格变化或方向）
            threshold: 信号阈值
            
        Returns:
            交易信号: 'BUY', 'SELL', 'HOLD'
        """
        if isinstance(prediction, torch.Tensor):
            prediction = prediction.cpu().numpy()
        
        if isinstance(prediction, np.ndarray):
            prediction = prediction.item() if prediction.size == 1 else prediction[0]
        
        # 根据预测值生成信号
        # print(f"预测值: {prediction} / 阈值: {threshold}")
        if prediction > threshold:
            return 'BUY'
        elif prediction < -threshold:
            return 'SELL'
        else:
            return 'HOLD'
    
    def _calculate_position_size(self, available_capital, price, leverage=None):
        """
        计算仓位大小
        
        Args:
            available_capital: 可用资金
            price: 当前价格
            leverage: 杠杆倍数
            
        Returns:
            仓位大小
        """
        current_leverage = leverage if leverage is not None else self.leverage
        
        # 计算最大可买入数量（考虑杠杆）
        max_position = (available_capital * current_leverage) / price
        
        # 使用固定比例的资金（例如90%）
        position_ratio = 0.9
        position_size = max_position * position_ratio
        
        return position_size
    
    def backtest(self, dataset, seq_len=30, commission=0.001, threshold=0.5,
                stop_loss=None, take_profit=None, print_interval=100):
        """
        执行回测
        
        Args:
            dataset: 数据集实例
            seq_len: 序列长度
            commission: 交易手续费率
            threshold: 交易信号阈值
            stop_loss: 止损比例 (None表示不使用止损)
            take_profit: 止盈比例 (None表示不使用止盈)
            print_interval: 打印间隔
            
        Returns:
            回测结果字典
        """
        # 确保模型处于评估模式
        if hasattr(self.model, 'eval'):
            self.model.eval()
        if hasattr(self.core_model, 'eval'):
            self.core_model.eval()
            
        # 移动模型到指定设备
        if hasattr(self.model, 'to'):
            self.model.to(self.device)
        if hasattr(self.core_model, 'to'):
            self.core_model.to(self.device)
        
        # 初始化回测状态
        capital = self.initial_capital
        position = 0
        entry_price = 0
        trades = []
        equity_curve = [capital]
        daily_returns = []
        positions_history = []
        signals = []
        
        # 记录保证金和可用资金
        margin = 0  # 保证金
        available_capital = capital  # 可用资金
        
        logger.info(f"开始回测，数据集大小: {len(dataset)}")
        
        # 遍历数据集进行回测
        for i, sample in enumerate(tqdm(dataset, desc="回测进度")):
            try:
                # 解析样本数据
                if isinstance(sample, (list, tuple)) and len(sample) >= 3:
                    embds, x, target = sample[0], sample[1], sample[2]
                else:
                    logger.warning(f"样本 {i} 格式不正确，跳过")
                    continue
                
                # 转换为张量并移动到设备
                if not isinstance(embds, torch.Tensor):
                    embds = torch.tensor(embds, dtype=torch.int64)
                if not isinstance(x, torch.Tensor):
                    x = torch.tensor(x, dtype=torch.float32)
                
                embds = embds.unsqueeze(0).to(self.device)
                x = x.unsqueeze(0).to(self.device)
                
                # 模型预测
                with torch.no_grad():
                    if self.model_type == 'PLFtsModel':
                        prediction = self.model(embds, x)
                    else:
                        prediction = self.core_model(embds, x)
                
                # 生成交易信号
                signal = self._generate_signal(prediction, threshold)
                signals.append(signal)
                
                # 获取当前价格（使用目标值作为当前价格的代理）
                if isinstance(target, torch.Tensor):
                    current_price = target.cpu().numpy().item()
                else:
                    current_price = float(target)
                
                # 模拟价格（如果target是变化率，需要转换为价格）
                if i == 0:
                    base_price = 100.0  # 假设基础价格
                    current_price = base_price * (1 + current_price)
                else:
                    # 使用前一个价格和变化率计算当前价格
                    prev_price = equity_curve[-1] if len(equity_curve) > 0 else 100.0
                    current_price = prev_price * (1 + current_price)
                
                current_datetime = datetime.now()  # 简化的时间戳
                
                # 检查止损和止盈
                if position != 0 and (stop_loss is not None or take_profit is not None):
                    if position > 0:  # 多头
                        unrealized_profit = (current_price - entry_price) * position
                        current_return = unrealized_profit / margin
                        
                        if (stop_loss is not None and current_return < -stop_loss) or \
                           (take_profit is not None and current_return > take_profit):
                            # 平仓
                            profit = unrealized_profit - commission * (entry_price + current_price) * position
                            capital = margin + available_capital + profit
                            
                            action = 'STOP_LOSS' if current_return < -stop_loss else 'TAKE_PROFIT'
                            trades.append({
                                'datetime': current_datetime,
                                'action': action,
                                'price': current_price,
                                'position': 0,
                                'capital': capital,
                                'profit': profit,
                                'return_rate': profit / margin
                            })
                            
                            position = 0
                            margin = 0
                            available_capital = capital
                    
                    elif position < 0:  # 空头
                        unrealized_profit = (entry_price - current_price) * (-position)
                        current_return = unrealized_profit / margin
                        
                        if (stop_loss is not None and current_return < -stop_loss) or \
                           (take_profit is not None and current_return > take_profit):
                            # 平仓
                            profit = unrealized_profit - commission * (entry_price + current_price) * (-position)
                            capital = margin + available_capital + profit
                            
                            action = 'STOP_LOSS' if current_return < -stop_loss else 'TAKE_PROFIT'
                            trades.append({
                                'datetime': current_datetime,
                                'action': action,
                                'price': current_price,
                                'position': 0,
                                'capital': capital,
                                'profit': profit,
                                'return_rate': profit / margin
                            })
                            
                            position = 0
                            margin = 0
                            available_capital = capital
                
                # 执行交易信号
                if signal == 'BUY' and position <= 0:
                    # 买入或平空
                    if position < 0:
                        # 先平空
                        profit = (entry_price - current_price) * (-position) - commission * (entry_price + current_price) * (-position)
                        capital = margin + available_capital + profit
                        available_capital = capital
                        margin = 0
                        
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'CLOSE_SHORT',
                            'price': current_price,
                            'position': 0,
                            'capital': capital,
                            'profit': profit
                        })
                    
                    # 开多
                    position_size = self._calculate_position_size(available_capital, current_price)
                    if position_size > 0:
                        margin = position_size * current_price / self.leverage
                        available_capital -= margin
                        position = position_size
                        entry_price = current_price
                        
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'BUY',
                            'price': current_price,
                            'position': position,
                            'margin': margin,
                            'available_capital': available_capital
                        })
                
                elif signal == 'SELL' and position >= 0:
                    # 卖出或平多
                    if position > 0:
                        # 先平多
                        profit = (current_price - entry_price) * position - commission * (entry_price + current_price) * position
                        capital = margin + available_capital + profit
                        available_capital = capital
                        margin = 0
                        
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'CLOSE_LONG',
                            'price': current_price,
                            'position': 0,
                            'capital': capital,
                            'profit': profit
                        })
                    
                    # 开空
                    position_size = self._calculate_position_size(available_capital, current_price)
                    if position_size > 0:
                        margin = position_size * current_price / self.leverage
                        available_capital -= margin
                        position = -position_size
                        entry_price = current_price
                        
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'SELL',
                            'price': current_price,
                            'position': position,
                            'margin': margin,
                            'available_capital': available_capital
                        })
                
                # 计算当前权益
                if position != 0:
                    if position > 0:
                        unrealized_profit = (current_price - entry_price) * position
                    else:
                        unrealized_profit = (entry_price - current_price) * (-position)
                    
                    current_equity = margin + available_capital + unrealized_profit
                else:
                    current_equity = available_capital
                
                equity_curve.append(current_equity)
                positions_history.append(position)
                
                # 计算日收益率
                if len(equity_curve) > 1:
                    daily_return = (equity_curve[-1] - equity_curve[-2]) / equity_curve[-2]
                    daily_returns.append(daily_return)
                
                # 打印进度信息
                if i % print_interval == 0:
                    logger.info(f"步骤 {i}: 信号={signal}, 仓位={position:.2f}, 权益={current_equity:.2f}")
                    
            except Exception as e:
                logger.error(f"处理样本 {i} 时出错: {e}")
                continue
        
        # 计算回测统计指标
        results = self._calculate_metrics(equity_curve, daily_returns, trades)
        results['signals'] = signals
        results['positions_history'] = positions_history
        results['equity_curve'] = equity_curve
        
        return results

    def _calculate_metrics(self, equity_curve, daily_returns, trades):
        """
        计算回测统计指标

        Args:
            equity_curve: 权益曲线
            daily_returns: 日收益率列表
            trades: 交易记录

        Returns:
            统计指标字典
        """
        if len(equity_curve) < 2:
            return {'error': '数据不足，无法计算指标'}

        # 基本指标
        total_return = (equity_curve[-1] - equity_curve[0]) / equity_curve[0]

        # 年化收益率（假设252个交易日）
        trading_days = len(equity_curve) - 1
        if trading_days > 0:
            try:
                # 限制收益率范围以避免溢出
                clamped_return = max(-0.99, min(total_return, 100.0))
                annualized_return = (1 + clamped_return) ** (252 / trading_days) - 1
            except (OverflowError, ValueError):
                # 如果仍然溢出，使用简化计算
                annualized_return = total_return * (252 / trading_days)
        else:
            annualized_return = 0

        # 最大回撤
        peak = equity_curve[0]
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        # 夏普比率
        if len(daily_returns) > 1:
            avg_return = np.mean(daily_returns)
            std_return = np.std(daily_returns)
            sharpe_ratio = (avg_return * 252) / (std_return * np.sqrt(252)) if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        # 交易统计
        total_trades = len(trades)
        profitable_trades = len([t for t in trades if t.get('profit', 0) > 0])
        win_rate = profitable_trades / total_trades if total_trades > 0 else 0

        # 平均盈利和亏损
        profits = [t.get('profit', 0) for t in trades if t.get('profit', 0) > 0]
        losses = [t.get('profit', 0) for t in trades if t.get('profit', 0) < 0]

        avg_profit = np.mean(profits) if profits else 0
        avg_loss = np.mean(losses) if losses else 0
        profit_loss_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else 0

        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'profit_loss_ratio': profit_loss_ratio,
            'final_capital': equity_curve[-1],
            'trades': trades
        }

    def visualize_backtest(self, results, save_path=None):
        """
        可视化回测结果

        Args:
            results: 回测结果字典
            save_path: 保存路径
        """
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        # 权益曲线
        equity_curve = results['equity_curve']
        axes[0].plot(equity_curve, label='权益曲线', linewidth=2)
        axes[0].axhline(y=self.initial_capital, color='r', linestyle='--', alpha=0.7, label='初始资金')
        axes[0].set_title('权益曲线')
        axes[0].set_ylabel('权益')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # 仓位历史
        positions_history = results['positions_history']
        axes[1].plot(positions_history, label='仓位', linewidth=1)
        axes[1].axhline(y=0, color='k', linestyle='-', alpha=0.5)
        axes[1].set_title('仓位历史')
        axes[1].set_ylabel('仓位大小')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # 信号分布
        signals = results['signals']
        signal_counts = pd.Series(signals).value_counts()
        axes[2].bar(signal_counts.index, signal_counts.values)
        axes[2].set_title('交易信号分布')
        axes[2].set_ylabel('次数')
        axes[2].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"图表已保存到: {save_path}")

        plt.show()

    def save_results(self, results, save_path):
        """
        保存回测结果到JSON文件

        Args:
            results: 回测结果字典
            save_path: 保存路径
        """
        # 创建可序列化的结果副本
        serializable_results = {}
        for key, value in results.items():
            if key in ['signals', 'positions_history', 'equity_curve']:
                # 转换为列表
                if isinstance(value, np.ndarray):
                    serializable_results[key] = value.tolist()
                else:
                    serializable_results[key] = value
            elif key == 'trades':
                # 处理交易记录中的datetime对象
                serializable_trades = []
                for trade in value:
                    trade_copy = trade.copy()
                    if 'datetime' in trade_copy:
                        trade_copy['datetime'] = trade_copy['datetime'].isoformat()
                    serializable_trades.append(trade_copy)
                serializable_results[key] = serializable_trades
            else:
                serializable_results[key] = value

        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        logger.info(f"回测结果已保存到: {save_path}")


def load_model(model_path, model_type='pytorch'):
    """
    加载模型

    Args:
        model_path: 模型路径
        model_type: 模型类型 ('pytorch' 或 'onnx')

    Returns:
        加载的模型
    """
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.warning(f"模型文件不存在: {model_path}")
        logger.info("将创建示例模型用于演示...")

        # 创建示例模型
        from pyqlab.models.fintimeseries.time_series_model2dr_v2 import TimeSeriesModel2drV2
        model = TimeSeriesModel2drV2(
            num_embeds=[72, 5, 11],
            num_channel=10,
            num_input=51,
            dropout=0.1,
            num_conv_layers=2,
            conv_channels=[32, 64],
            use_residual=True,
            use_attention=False,
            use_temporal_conv=True,
            num_outputs=1,
            probabilistic=False,
            multi_task=False,
            out_channels=(24, 48, 256, 128),
            ins_nums=(0, 51, 51, 17),
            inference_mode=True
        )
        model.eval()
        logger.info("已创建示例模型（注意：这是随机初始化的模型，仅用于演示）")
        return model

    if model_type == 'pytorch':
        try:
            if model_path.endswith('.ckpt'):
                # 加载Lightning checkpoint
                model = PLFtsModel.load_from_checkpoint(model_path)
                logger.info(f"已加载Lightning模型: {model_path}")
            else:
                # 加载普通PyTorch模型
                model = torch.load(model_path, map_location='cpu')
                logger.info(f"已加载PyTorch模型: {model_path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    elif model_type == 'onnx':
        try:
            import onnxruntime as ort
            model = ort.InferenceSession(model_path)
            logger.info(f"已加载ONNX模型: {model_path}")
        except Exception as e:
            logger.error(f"加载ONNX模型失败: {e}")
            raise
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

    return model


def main():
    parser = argparse.ArgumentParser(description='TimeSeriesModel2drV2模型回测')

    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--model_type', type=str, default='pytorch', choices=['pytorch', 'onnx'], help='模型类型')

    # 数据参数
    parser.add_argument('--data_path', type=str, default='e:/featdata/main', help='数据路径')
    parser.add_argument('--ds_files', type=str, default='["main.2024"]', help='数据文件列表')
    parser.add_argument('--fut_codes', type=str, default='["IF", "IH", "IC"]', help='期货代码列表')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--batch_size', type=int, default=1, help='批次大小')

    # 回测参数
    parser.add_argument('--initial_capital', type=float, default=10000.0, help='初始资金')
    parser.add_argument('--commission', type=float, default=0.001, help='手续费率')
    parser.add_argument('--threshold', type=float, default=0.5, help='交易信号阈值')
    parser.add_argument('--stop_loss', type=float, default=None, help='止损比例')
    parser.add_argument('--take_profit', type=float, default=None, help='止盈比例')
    parser.add_argument('--leverage', type=float, default=1.0, help='杠杆倍数')
    parser.add_argument('--print_interval', type=int, default=100, help='打印间隔')

    # 输出参数
    parser.add_argument('--output_dir', type=str, default='./backtest_results', help='输出目录')
    parser.add_argument('--save_chart', action='store_true', help='保存图表')

    args = parser.parse_args()

    # 解析列表参数
    import ast
    args.ds_files = ast.literal_eval(args.ds_files)
    args.fut_codes = ast.literal_eval(args.fut_codes)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载模型
    logger.info("加载模型...")
    model = load_model(args.model_path, args.model_type)

    # 加载数据集
    logger.info("加载数据集...")

    # 检查数据路径是否存在
    if not os.path.exists(args.data_path):
        logger.warning(f"数据路径不存在: {args.data_path}")
        logger.info("将使用模拟数据进行演示...")

        # 使用模拟数据集
        from pyqlab.models.fintimeseries.test_backtest import MockDataset
        dataset = MockDataset(size=500, seq_len=args.seq_len)
        logger.info(f"已创建模拟数据集，大小: {len(dataset)}")
    else:
        try:
            dataset = get_dataset(
                ds_files=args.ds_files,
                ins_nums=(0, 51, 51, 17),
                fut_codes=args.fut_codes,
                data_path=args.data_path,
                model_type=0,  # FTS模型类型
                seq_len=args.seq_len,
                pred_len=1
            )
            dataset.load_data()
            # 如果数据集太大，只使用一部分进行演示
            if len(dataset) > 500:
                logger.info("数据集较大，仅使用前1000个样本进行演示")
                dataset = torch.utils.data.Subset(dataset, range(1000))
            logger.info(f"成功加载真实数据集，大小: {len(dataset)}")
        except Exception as e:
            logger.warning(f"加载真实数据失败: {e}")
            logger.info("将使用模拟数据进行演示...")

            # 使用模拟数据集作为备选
            from pyqlab.models.fintimeseries.test_backtest import MockDataset
            dataset = MockDataset(size=500, seq_len=args.seq_len)
            logger.info(f"已创建模拟数据集，大小: {len(dataset)}")

    # 创建回测器
    backtester = TimeSeriesBacktester(
        model=model,
        initial_capital=args.initial_capital,
        leverage=args.leverage
    )

    # 执行回测
    logger.info("开始回测...")
    start_time = time.time()

    results = backtester.backtest(
        dataset=dataset,
        seq_len=args.seq_len,
        commission=args.commission,
        threshold=args.threshold,
        stop_loss=args.stop_loss,
        take_profit=args.take_profit,
        print_interval=args.print_interval
    )

    end_time = time.time()
    logger.info(f"回测完成，耗时: {end_time - start_time:.2f}秒")

    # 打印回测结果
    logger.info("\n=== 回测结果 ===")
    logger.info(f"总收益率: {results['total_return']:.4f} ({results['total_return']*100:.2f}%)")
    logger.info(f"年化收益率: {results['annualized_return']:.4f} ({results['annualized_return']*100:.2f}%)")
    logger.info(f"最大回撤: {results['max_drawdown']:.4f} ({results['max_drawdown']*100:.2f}%)")
    logger.info(f"夏普比率: {results['sharpe_ratio']:.4f}")
    logger.info(f"总交易次数: {results['total_trades']}")
    logger.info(f"胜率: {results['win_rate']:.4f} ({results['win_rate']*100:.2f}%)")
    logger.info(f"盈亏比: {results['profit_loss_ratio']:.4f}")
    logger.info(f"最终资金: {results['final_capital']:.2f}")

    # 保存结果
    results_path = os.path.join(args.output_dir, 'backtest_results.json')
    backtester.save_results(results, results_path)

    # 可视化结果
    if args.save_chart:
        chart_path = os.path.join(args.output_dir, 'backtest_chart.png')
        backtester.visualize_backtest(results, chart_path)
    else:
        backtester.visualize_backtest(results)

    logger.info(f"回测完成，结果已保存到: {args.output_dir}")


if __name__ == "__main__":
    main()
