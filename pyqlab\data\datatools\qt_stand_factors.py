# ### 标准化（Standardization）特征数据
# 
# 特征数据的标准化和归一化是在量化投资中常用的数据预处理技术，其主要目的是消除不同特征之间的尺度差异，使其具有相似的尺度范围，以便更好地适应模型的训练和优化过程。以下是标准化和归一化的作用和常见方法：
# 
# 作用：
# - 帮助优化算法更快地收敛：标准化和归一化可以使不同特征之间的尺度范围相似，使优化算法能够更快地找到最优解，避免因不同尺度带来的优化困难。
# 
# - 防止某些特征对模型的主导影响：如果某个特征的数值范围较大，可能会对模型的训练产生过大的影响，而忽略了其他特征的作用。标准化和归一化可以平衡不同特征之间的影响，确保模型能够全面考虑各个特征。
# 
# - 提高模型的稳定性和鲁棒性：标准化和归一化可以减少异常值和噪声对模型的干扰，提高模型的稳定性和鲁棒性。

# #### 1.Factors
# - lf: long period factors
# - sf: short period factors
# - ct: market and portfolio context factors

import pandas as pd
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES
from argparse import ArgumentParser

# feat_path = 'e:/featdata'

class FactorMeanStd:

    def __init__(self, feat_path: str, save_path: str, fut_years: list):
        self.feat_path = feat_path
        self.save_path = save_path
        self.fut_years = fut_years
        self.factor_types = ['lf', 'sf', 'mf', 'ct']

    def get_factor_cols(self, factor_type="lf"):
        """
        因子列名称
        """
        col_names = []
        if factor_type == "lf":
            for name in ALL_FACTOR_NAMES:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "sf" or factor_type == "mf":
            for name in ALL_FACTOR_NAMES: # SEL_SHORT_FACTOR_NAMES:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append(f"{name}_1")
                    col_names.append(f"{name}_2")
                else:
                    col_names.append(f"{name}_2")

        if factor_type == "ct":
            col_names.extend(SNAPSHOT_CONTEXT)

        return col_names

    def get_factor_df(self, year, factor_type="lf", suffix="main"):
        df = pd.read_parquet(f'{self.feat_path}/ffs_{factor_type}.{suffix}.{year}.parquet')
        # df.dropna(axis=0, how='any', inplace=True)
        # print(year, df.shape)
        df.fillna(0.0, inplace=True)
        if 'RSI_2' in df.columns:
            df = df[df['RSI_2'] != 0.0]
        if 'FAST_QH_NATR_ZSCORE' in df.columns:
            df = df[df['FAST_QH_NATR_ZSCORE'] != 0.0]
        if 'change' in df.columns:
            df['change'] = df['change'].astype(float)
            df = df[(df['change'] <= 0.01) & (df['change'] >= -0.01)]
        # 剔除BAND_EXPAND_2大于20的极端数据
        # 通常是主力合约换月或长假开盘后有较大的跳空导致的
        if 'BAND_EXPAND_2' in df.columns:
            df = df[df['BAND_EXPAND_2'] < 15.0] 
        # print(year, df.shape)
        # 删除列 change，date
        if 'change' in df.columns:
            df.drop(['change', 'date'], axis=1, inplace=True)
        elif 'date' in df.columns:
            df.drop(['date'], axis=1, inplace=True)
        return df

    def calc_factor_mean_std(self, year, factor_type="lf", suffix="main"):
        df = self.get_factor_df(year, factor_type, suffix)
        # 将因子数据按CODE分组求均值和标准差
        df_mean = df.groupby('code').mean()
        df_std = df.groupby('code').std()
        return df_mean, df_std


    def get_factors_mean_std(self, type="lf", suffix="main"):
        if type == "lf" or type == "sf" or type == "mf":
            # 计算商品期货因子数据的均值和标准差
            df_mean = {}
            df_std = {}
            for year in self.fut_years:
                df_mean[year], df_std[year] = self.calc_factor_mean_std(year, type, suffix)
            # 将所有因子数据的mean和std合并求均值
            df_mean_all = pd.concat(df_mean.values()).groupby('code').mean()
            df_std_all = pd.concat(df_std.values()).groupby('code').mean()
            df_mean_all = df_mean_all[self.get_factor_cols(type)] # 确保列的顺序一致
            df_std_all = df_std_all[self.get_factor_cols(type)] # 确保列的顺序一致
            return df_mean_all, df_std_all
        elif type == "ct":
            if suffix == "sf":
                return pd.DataFrame(), pd.DataFrame()
            # 计算商品期货市场因子数据的均值和标准差
            dfs = pd.DataFrame()
            df_mean = {}
            df_std = {}
            for year in self.fut_years:
                df = self.get_factor_df(year, type, suffix)
                df = df[['code']+ self.get_factor_cols('ct')] # 确保列的顺序一致
                df_mean[year] = df.loc[:,'FAST_RSI':].mean()
                df_std[year] = df.loc[:,'FAST_RSI':].std()
                dfs = pd.concat([dfs, df])
            # 多年Dict合并为DataFrame并行列交换
            df_mean_all = pd.DataFrame(df_mean).T
            df_std_all = pd.DataFrame(df_std).T
            # 用df_mean_all和df_std_all填充df_mean和df_std
            df_mean = dfs.groupby('code').mean()
            df_std = dfs.groupby('code').std()
            df_mean.loc[:,'FAST_RSI':] = df_mean_all.mean().values
            df_std.loc[:,'FAST_RSI':] = df_std_all.mean().values
            df_mean = df_mean[self.get_factor_cols(type)] # 确保列的顺序一致
            df_std = df_std[self.get_factor_cols(type)] # 确保列的顺序一致
            return df_mean, df_std
        else:
            raise ValueError("type must be lf or sf")


    def get_all_factors_mean_std(self, suffixs=["main", "sf"]):
        for type in self.factor_types:
            df_mean = pd.DataFrame()
            df_std = pd.DataFrame()
            for suffix in suffixs:
                fut_mean, fut_std = self.get_factors_mean_std(type, suffix=suffix)
                print(suffix, type, fut_mean.shape)
                df_mean = pd.concat([df_mean, fut_mean])
                df_std = pd.concat([df_std, fut_std])
            print("all: ", type, df_mean.shape)
            df_mean.to_csv(f'{self.save_path}/{type}_mean.csv')
            df_std.to_csv(f'{self.save_path}/{type}_std.csv')


    # 两个dataframe合并，不要覆盖第一个dataframe已有code字段的数据
    def mergers_mean_std_files(self, data_path1, data_path2):
        factor_types = ['lf', 'sf', 'mf', ] # 'ct'
        for type in factor_types:
            df_mean1 = pd.read_csv(f'{data_path1}/{type}_mean.csv')
            df_mean2 = pd.read_csv(f'{data_path2}/{type}_mean.csv')
            df_mean = pd.concat([df_mean1, df_mean2], ignore_index=True)
            df_mean.drop_duplicates(subset=['code'], keep='last', inplace=True)
            df_mean.to_csv(f'{data_path1}/{type}_mean.csv', index=False)
            print(f"{type}_mean: ", type, df_mean.shape)
            df_std1 = pd.read_csv(f'{data_path1}/{type}_std.csv')
            df_std2 = pd.read_csv(f'{data_path2}/{type}_std.csv')
            df_std = pd.concat([df_std1, df_std2], ignore_index=True)
            df_std.drop_duplicates(subset=['code'], keep='last', inplace=True)
            df_std.to_csv(f'{data_path1}/{type}_std.csv', index=False)
            print(f"{type}_std: ", df_std.shape)
        

def main(args):
    fms = FactorMeanStd(args.feat_path, args.save_path, args.fut_years)
    if args.mergers:
        fms.mergers_mean_std_files(data_path1='e:/featdata/main',
                                data_path2='e:/featdata/sf')
    else:
        fms.get_all_factors_mean_std(args.suffixs)

if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument('--mergers', default=False, type=bool)
    parser.add_argument('--suffixs', default=["top"], type=list)
    parser.add_argument('--feat_path', default='f:/featdata/top', type=str)
    parser.add_argument('--save_path', default='f:/featdata/top', type=str)
    parser.add_argument('--fut_years', default=['2024', '2025'], type=list)

    # parser.add_argument('--suffixs', default=["sf"], type=list)
    # parser.add_argument('--feat_path', default='e:/featdata/sf', type=str)
    # parser.add_argument('--save_path', default='e:/featdata/sf', type=str)
    # parser.add_argument('--fut_years', default=['2019','2020','2021','2022','2023'], type=list)
    args = parser.parse_args()

    main(args)

    




