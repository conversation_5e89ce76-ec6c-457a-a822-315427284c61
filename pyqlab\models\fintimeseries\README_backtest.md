# TimeSeriesModel2drV2 模型回测系统

## 概述

本回测系统专门为 `TimeSeriesModel2drV2` 模型设计，支持对训练好的模型进行全面的回测评估，包括收益率分析、风险指标计算和可视化展示。

## 功能特性

- ✅ 支持 PyTorch 和 ONNX 格式模型
- ✅ 完整的交易模拟（开仓、平仓、止损、止盈）
- ✅ 杠杆交易支持
- ✅ 多种风险指标计算（夏普比率、最大回撤等）
- ✅ 可视化结果展示
- ✅ 详细的交易记录和统计
- ✅ 配置文件支持

## 快速开始

### 1. 基本使用

```bash
# 使用PyTorch模型运行回测
python backtest_time_series_model2dr_v2.py --model_path ./model/best_model.ckpt

# 使用ONNX模型运行回测
python backtest_time_series_model2dr_v2.py --model_path ./model/best_model.onnx --model_type onnx

# 或者使用批处理脚本
run_backtest_en.bat ./model/best_model.ckpt
```

### 2. 完整参数示例

```bash
python backtest_time_series_model2dr_v2.py \
    --model_path ./model/best_model.ckpt \
    --model_type pytorch \
    --data_path e:/featdata/main \
    --ds_files "[\"main.2024\"]" \
    --fut_codes "[\"IF\", \"IH\", \"IC\"]" \
    --seq_len 30 \
    --initial_capital 10000 \
    --commission 0.001 \
    --threshold 0.5 \
    --stop_loss 0.05 \
    --take_profit 0.10 \
    --leverage 2.0 \
    --output_dir ./backtest_results \
    --save_chart
```

## 参数说明

### 模型参数
- `--model_path`: 模型文件路径（必需）
- `--model_type`: 模型类型，支持 `pytorch` 或 `onnx`

### 数据参数
- `--data_path`: 数据根目录路径
- `--ds_files`: 数据文件列表，JSON格式字符串
- `--fut_codes`: 期货代码列表，JSON格式字符串
- `--seq_len`: 输入序列长度
- `--batch_size`: 批次大小（通常设为1）

### 回测参数
- `--initial_capital`: 初始资金（默认10000）
- `--commission`: 交易手续费率（默认0.001）
- `--threshold`: 交易信号阈值（默认0.5）
- `--stop_loss`: 止损比例（可选）
- `--take_profit`: 止盈比例（可选）
- `--leverage`: 杠杆倍数（默认1.0）
- `--print_interval`: 打印间隔（默认100）

### 输出参数
- `--output_dir`: 输出目录（默认./backtest_results）
- `--save_chart`: 是否保存图表

## 输出结果

回测完成后，会在输出目录中生成以下文件：

1. **backtest_results.json**: 详细的回测结果数据
2. **backtest_chart.png**: 可视化图表（如果启用）

### 回测指标

- **总收益率**: 整个回测期间的总收益率
- **年化收益率**: 按年化计算的收益率
- **最大回撤**: 最大回撤比例
- **夏普比率**: 风险调整后的收益率
- **胜率**: 盈利交易占总交易的比例
- **盈亏比**: 平均盈利与平均亏损的比值
- **交易次数**: 总交易次数

### 可视化图表

生成的图表包含三个子图：
1. **权益曲线**: 显示资金随时间的变化
2. **仓位历史**: 显示持仓情况的变化
3. **信号分布**: 显示各种交易信号的分布

## 配置文件使用

可以使用配置文件来管理复杂的参数设置：

```json
{
  "model": {
    "path": "./model/best_model.ckpt",
    "type": "pytorch"
  },
  "data": {
    "path": "e:/featdata/main",
    "files": ["main.2024"],
    "fut_codes": ["IF", "IH", "IC"],
    "seq_len": 30
  },
  "backtest": {
    "initial_capital": 10000.0,
    "commission": 0.001,
    "threshold": 0.5,
    "leverage": 1.0
  }
}
```

## 交易逻辑

### 信号生成
模型预测值通过阈值判断生成交易信号：
- 预测值 > 阈值 → BUY 信号
- 预测值 < -阈值 → SELL 信号
- 其他情况 → HOLD 信号

### 仓位管理
- 支持多空双向交易
- 自动平仓机制（反向信号时先平仓再开新仓）
- 杠杆交易支持（保证金计算）

### 风险控制
- 止损：当亏损达到设定比例时自动平仓
- 止盈：当盈利达到设定比例时自动平仓
- 手续费：每次交易扣除手续费

## 注意事项

1. **数据格式**: 确保数据集格式与模型训练时一致
2. **模型兼容性**: 支持 PLFtsModel 和原生 TimeSeriesModel2drV2
3. **内存使用**: 大数据集回测时注意内存使用情况
4. **参数调优**: 建议先用小数据集测试参数设置

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认模型文件格式（.ckpt 或 .pth）

2. **数据加载错误**
   - 检查数据路径和文件列表
   - 确认期货代码列表格式正确

3. **内存不足**
   - 减少批次大小
   - 使用较小的数据集进行测试

4. **预测结果异常**
   - 检查模型是否正确加载
   - 确认输入数据格式与训练时一致

## 扩展功能

### 自定义信号生成器
可以通过修改 `_generate_signal` 方法来实现自定义的信号生成逻辑。

### 多策略回测
可以扩展脚本支持多个策略的同时回测和比较。

### 实时回测
可以集成实时数据源进行实时回测。

## 联系支持

如有问题或建议，请联系开发团队或提交 Issue。
